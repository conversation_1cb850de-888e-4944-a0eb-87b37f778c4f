from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from services.chat_service import chat_service
import logging

chat_bp = Blueprint('chat', __name__)

@chat_bp.route('/chat')
def chat_page():
    """聊天页面"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))
    
    return render_template('chat.html')

@chat_bp.route('/api/chat', methods=['POST'])
def chat_api():
    """聊天API接口"""
    if 'user_id' not in session:
        return jsonify({
            'success': False,
            'error': '请先登录'
        }), 401
    
    try:
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({
                'success': False,
                'error': '消息内容不能为空'
            }), 400
        
        user_message = data['message'].strip()
        if not user_message:
            return jsonify({
                'success': False,
                'error': '消息内容不能为空'
            }), 400
        
        # 获取对话历史（可选）
        conversation_history = data.get('history', [])
        
        # 获取用户上下文信息
        user_id = session['user_id']
        context = chat_service.get_material_context(user_id)
        
        # 将上下文信息添加到用户消息中
        enhanced_message = f"用户上下文：{context}\n\n用户问题：{user_message}"
        
        # 调用聊天服务
        result = chat_service.send_message(enhanced_message, conversation_history)
        
        if result['success']:
            return jsonify({
                'success': True,
                'reply': result['reply'],
                'timestamp': None  # 可以添加时间戳
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        logging.error(f"聊天API错误: {e}")
        return jsonify({
            'success': False,
            'error': '系统错误，请稍后重试'
        }), 500

@chat_bp.route('/api/chat/clear', methods=['POST'])
def clear_chat():
    """清空聊天记录"""
    if 'user_id' not in session:
        return jsonify({
            'success': False,
            'error': '请先登录'
        }), 401
    
    try:
        # 这里可以添加清空用户聊天记录的逻辑
        # 目前只是返回成功状态
        return jsonify({
            'success': True,
            'message': '聊天记录已清空'
        })
        
    except Exception as e:
        logging.error(f"清空聊天记录错误: {e}")
        return jsonify({
            'success': False,
            'error': '清空失败，请稍后重试'
        }), 500

@chat_bp.route('/api/chat/suggestions')
def get_suggestions():
    """获取聊天建议"""
    if 'user_id' not in session:
        return jsonify({
            'success': False,
            'error': '请先登录'
        }), 401
    
    try:
        # 根据用户角色提供不同的建议
        user_role = session.get('role', 'user')
        
        if user_role == 'admin':
            suggestions = [
                "如何查看物资库存统计？",
                "怎样审核物资申请？",
                "如何生成物资使用报表？",
                "怎样添加新的物资类型？",
                "如何管理用户权限？"
            ]
        else:
            suggestions = [
                "如何申请物资？",
                "怎样查看我的申请状态？",
                "如何查看可用物资列表？",
                "怎样查看我的分配记录？",
                "如何修改个人信息？"
            ]
        
        return jsonify({
            'success': True,
            'suggestions': suggestions
        })
        
    except Exception as e:
        logging.error(f"获取聊天建议错误: {e}")
        return jsonify({
            'success': False,
            'error': '获取建议失败'
        }), 500
