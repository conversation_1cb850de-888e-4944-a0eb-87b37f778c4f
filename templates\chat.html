{% extends "base.html" %}

{% block title %}智能助手 - 金融企业物资管理系统{% endblock %}

{% block extra_css %}
<style>
.chat-container {
    max-width: 800px;
    margin: 20px auto;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: #007bff;
    color: white;
    padding: 15px 20px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.bot {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.user .message-content {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 5px;
}

.message.bot .message-content {
    background: white;
    color: #333;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 5px;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin: 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}

.user-avatar {
    background: #28a745;
}

.bot-avatar {
    background: #6c757d;
}

.chat-input-container {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background: white;
    border-radius: 0 0 10px 10px;
}

.chat-input-form {
    display: flex;
    gap: 10px;
}

.chat-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    outline: none;
    font-size: 14px;
}

.chat-input:focus {
    border-color: #007bff;
}

.send-btn {
    padding: 10px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
}

.send-btn:hover {
    background: #0056b3;
}

.send-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.suggestions {
    margin-bottom: 15px;
}

.suggestion-item {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 5px 12px;
    margin: 3px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 12px;
    border: 1px solid #dee2e6;
}

.suggestion-item:hover {
    background: #007bff;
    color: white;
}

.typing-indicator {
    display: none;
    padding: 10px 15px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 18px;
    border-bottom-left-radius: 5px;
    max-width: 70%;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #999;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
    border: 1px solid #f5c6cb;
}

.clear-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
}

.clear-btn:hover {
    background: #5a6268;
}
</style>
{% endblock %}

{% block content %}
<div class="chat-container">
    <div class="chat-header">
        <h3>🤖 智能助手</h3>
        <button class="clear-btn" onclick="clearChat()">清空对话</button>
    </div>
    
    <div class="chat-messages" id="chatMessages">
        <div class="message bot">
            <div class="message-avatar bot-avatar">🤖</div>
            <div class="message-content">
                您好！我是物资管理系统的智能助手，可以帮助您解答系统使用问题、物资管理流程等。请问有什么可以帮助您的吗？
            </div>
        </div>
        
        <div class="suggestions" id="suggestions">
            <!-- 建议将通过JavaScript动态加载 -->
        </div>
    </div>
    
    <div class="chat-input-container">
        <form class="chat-input-form" onsubmit="sendMessage(event)">
            <input type="text" class="chat-input" id="messageInput" 
                   placeholder="请输入您的问题..." autocomplete="off">
            <button type="submit" class="send-btn" id="sendBtn">发送</button>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let conversationHistory = [];

// 页面加载时获取建议
document.addEventListener('DOMContentLoaded', function() {
    loadSuggestions();
});

// 加载聊天建议
function loadSuggestions() {
    fetch('/api/chat/suggestions')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySuggestions(data.suggestions);
            }
        })
        .catch(error => {
            console.error('加载建议失败:', error);
        });
}

// 显示建议
function displaySuggestions(suggestions) {
    const suggestionsContainer = document.getElementById('suggestions');
    suggestionsContainer.innerHTML = '';
    
    suggestions.forEach(suggestion => {
        const item = document.createElement('span');
        item.className = 'suggestion-item';
        item.textContent = suggestion;
        item.onclick = () => {
            document.getElementById('messageInput').value = suggestion;
            suggestionsContainer.style.display = 'none';
        };
        suggestionsContainer.appendChild(item);
    });
}

// 发送消息
function sendMessage(event) {
    event.preventDefault();
    
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // 显示用户消息
    addMessage('user', message);
    
    // 清空输入框
    messageInput.value = '';
    
    // 禁用发送按钮
    const sendBtn = document.getElementById('sendBtn');
    sendBtn.disabled = true;
    
    // 显示输入指示器
    showTypingIndicator();
    
    // 发送到后端
    fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: message,
            history: conversationHistory
        })
    })
    .then(response => response.json())
    .then(data => {
        hideTypingIndicator();
        
        if (data.success) {
            addMessage('bot', data.reply);
            
            // 更新对话历史
            conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: data.reply }
            );
            
            // 限制历史记录长度
            if (conversationHistory.length > 20) {
                conversationHistory = conversationHistory.slice(-20);
            }
        } else {
            showError(data.error || '发送失败，请稍后重试');
        }
    })
    .catch(error => {
        hideTypingIndicator();
        showError('网络错误，请检查连接后重试');
        console.error('发送消息失败:', error);
    })
    .finally(() => {
        sendBtn.disabled = false;
    });
}

// 添加消息到聊天界面
function addMessage(type, content) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    const avatar = document.createElement('div');
    avatar.className = `message-avatar ${type}-avatar`;
    avatar.textContent = type === 'user' ? '👤' : '🤖';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = content;
    
    if (type === 'user') {
        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(avatar);
    } else {
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(contentDiv);
    }
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 显示输入指示器
function showTypingIndicator() {
    const messagesContainer = document.getElementById('chatMessages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot';
    typingDiv.id = 'typingIndicator';
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar bot-avatar';
    avatar.textContent = '🤖';
    
    const indicator = document.createElement('div');
    indicator.className = 'typing-indicator';
    indicator.style.display = 'block';
    
    const dots = document.createElement('div');
    dots.className = 'typing-dots';
    for (let i = 0; i < 3; i++) {
        const dot = document.createElement('div');
        dot.className = 'typing-dot';
        dots.appendChild(dot);
    }
    
    indicator.appendChild(dots);
    typingDiv.appendChild(avatar);
    typingDiv.appendChild(indicator);
    messagesContainer.appendChild(typingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 隐藏输入指示器
function hideTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// 显示错误消息
function showError(message) {
    const messagesContainer = document.getElementById('chatMessages');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    messagesContainer.appendChild(errorDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 清空聊天记录
function clearChat() {
    if (confirm('确定要清空所有聊天记录吗？')) {
        fetch('/api/chat/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = `
                    <div class="message bot">
                        <div class="message-avatar bot-avatar">🤖</div>
                        <div class="message-content">
                            聊天记录已清空。有什么可以帮助您的吗？
                        </div>
                    </div>
                `;
                conversationHistory = [];
                loadSuggestions();
            }
        })
        .catch(error => {
            console.error('清空聊天记录失败:', error);
        });
    }
}

// 回车发送消息
document.getElementById('messageInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage(e);
    }
});
</script>
{% endblock %}
