#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import sys
import os

def create_demo_database():
    """创建演示数据库和数据"""
    
    # 尝试连接MySQL（不指定数据库）
    connection_attempts = [
        {'host': 'localhost', 'user': 'root', 'password': ''},
        {'host': 'localhost', 'user': 'root', 'password': 'root'},
        {'host': 'localhost', 'user': 'root', 'password': '123456'},
        {'host': 'localhost', 'user': 'root', 'password': 'mysql'},
        {'host': 'localhost', 'user': 'root', 'password': 'password'},
    ]
    
    conn = None
    working_config = None
    
    for attempt in connection_attempts:
        try:
            print(f"尝试连接MySQL: user={attempt['user']}, password={'*' * len(attempt['password']) if attempt['password'] else '(空)'}")
            conn = pymysql.connect(**attempt)
            working_config = attempt
            print("✓ 连接成功！")
            break
        except pymysql.Error as e:
            print(f"✗ 连接失败: {e}")
            continue
    
    if not conn:
        print("\n无法连接到MySQL。让我们创建一个简化的演示版本...")
        return create_simple_demo()
    
    try:
        cursor = conn.cursor()
        
        # 创建数据库
        print("\n创建goods数据库...")
        cursor.execute("DROP DATABASE IF EXISTS goods")
        cursor.execute("CREATE DATABASE goods CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.execute("USE goods")
        print("✓ goods数据库创建成功")
        
        # 创建表
        print("创建数据表...")
        
        # 科室表
        cursor.execute("""
            CREATE TABLE departments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 用户表
        cursor.execute("""
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                real_name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'employee') NOT NULL DEFAULT 'employee',
                department VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 物资表
        cursor.execute("""
            CREATE TABLE materials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                category VARCHAR(100),
                price DECIMAL(10,2) NOT NULL,
                quantity INT DEFAULT 1,
                remaining_quantity INT DEFAULT 1,
                status ENUM('in_use', 'scrapped', 'available') DEFAULT 'available',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 申请表
        cursor.execute("""
            CREATE TABLE material_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                material_id INT,
                quantity INT NOT NULL,
                reason TEXT,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (material_id) REFERENCES materials(id)
            )
        """)
        
        print("✓ 数据表创建成功")
        
        # 插入演示数据
        print("插入演示数据...")
        
        # 插入科室
        cursor.execute("INSERT INTO departments (name, description) VALUES ('财务部', '负责财务管理')")
        cursor.execute("INSERT INTO departments (name, description) VALUES ('人事部', '负责人力资源管理')")
        cursor.execute("INSERT INTO departments (name, description) VALUES ('技术部', '负责技术开发')")
        
        # 插入用户
        cursor.execute("""
            INSERT INTO users (username, password, real_name, role, department) 
            VALUES ('admin', 'admin123', '管理员', 'admin', '财务部')
        """)
        cursor.execute("""
            INSERT INTO users (username, password, real_name, role, department) 
            VALUES ('user1', 'user123', '张三', 'employee', '人事部')
        """)
        cursor.execute("""
            INSERT INTO users (username, password, real_name, role, department) 
            VALUES ('user2', 'user123', '李四', 'employee', '技术部')
        """)
        
        # 插入物资
        cursor.execute("""
            INSERT INTO materials (name, category, price, quantity, remaining_quantity, description) 
            VALUES ('办公桌', '办公家具', 500.00, 10, 8, '标准办公桌')
        """)
        cursor.execute("""
            INSERT INTO materials (name, category, price, quantity, remaining_quantity, description) 
            VALUES ('笔记本电脑', '电子设备', 5000.00, 5, 3, '联想ThinkPad')
        """)
        cursor.execute("""
            INSERT INTO materials (name, category, price, quantity, remaining_quantity, description) 
            VALUES ('打印纸', '办公用品', 25.00, 100, 85, 'A4打印纸')
        """)
        
        conn.commit()
        print("✓ 演示数据插入成功")
        
        # 更新配置文件
        print("更新配置文件...")
        update_config_file(working_config)
        
        print(f"\n✓ 数据库设置完成！")
        print(f"数据库连接信息：")
        print(f"  主机: {working_config['host']}")
        print(f"  用户: {working_config['user']}")
        print(f"  密码: {'*' * len(working_config['password']) if working_config['password'] else '(空)'}")
        print(f"  数据库: goods")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库设置失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def update_config_file(working_config):
    """更新配置文件"""
    config_content = f"""import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # 数据库配置
    DB_CONFIG = {{
        'host': '{working_config['host']}',
        'user': '{working_config['user']}',
        'password': '{working_config['password']}',
        'database': 'goods',
        'charset': 'utf8mb4'
    }}
    
    # DeepSeek API配置
    DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY') or 'sk-your-deepseek-api-key-here'
    DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions'
"""
    
    with open('config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)

def create_simple_demo():
    """创建简化的演示版本"""
    print("创建简化演示版本...")
    print("请手动设置MySQL数据库，或联系系统管理员。")
    return False

if __name__ == "__main__":
    print("=== 创建演示数据库 ===")
    if create_demo_database():
        print("\n=== 数据库创建完成！ ===")
        print("现在可以运行应用了：python app.py")
        print("默认登录账号：")
        print("  管理员: admin / admin123")
        print("  普通用户: user1 / user123")
    else:
        print("\n=== 数据库创建失败 ===")
        sys.exit(1)
