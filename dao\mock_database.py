#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from datetime import datetime

# 避免循环导入，直接在这里定义User类
class MockUser:
    def __init__(self, id=None, username=None, password=None, real_name=None,
                 role=None, department=None, email=None, phone=None, status='active'):
        self.id = id
        self.username = username
        self.password = password
        self.real_name = real_name
        self.role = role
        self.department = department
        self.email = email
        self.phone = phone
        self.status = status

class MockDatabase:
    """模拟数据库类，用于演示"""
    
    def __init__(self):
        self._users = [
            {
                'id': 1,
                'username': 'admin',
                'password': 'admin123',
                'real_name': '管理员',
                'role': 'admin',
                'department': '财务部',
                'email': '<EMAIL>',
                'phone': '13800138000',
                'status': 'active'
            },
            {
                'id': 2,
                'username': 'user1',
                'password': 'user123',
                'real_name': '张三',
                'role': 'employee',
                'department': '人事部',
                'email': 'zhang<PERSON>@company.com',
                'phone': '13800138001',
                'status': 'active'
            },
            {
                'id': 3,
                'username': 'user2',
                'password': 'user123',
                'real_name': '李四',
                'role': 'employee',
                'department': '技术部',
                'email': '<EMAIL>',
                'phone': '13800138002',
                'status': 'active'
            }
        ]
        
        self._materials = [
            {
                'id': 1,
                'name': '办公桌',
                'category': '办公家具',
                'price': 500.00,
                'quantity': 10,
                'remaining_quantity': 8,
                'status': 'available',
                'description': '标准办公桌',
                'created_at': datetime.now()
            },
            {
                'id': 2,
                'name': '笔记本电脑',
                'category': '电子设备',
                'price': 5000.00,
                'quantity': 5,
                'remaining_quantity': 3,
                'status': 'available',
                'description': '联想ThinkPad',
                'created_at': datetime.now()
            },
            {
                'id': 3,
                'name': '打印纸',
                'category': '办公用品',
                'price': 25.00,
                'quantity': 100,
                'remaining_quantity': 85,
                'status': 'available',
                'description': 'A4打印纸',
                'created_at': datetime.now()
            }
        ]
        
        self._requests = [
            {
                'id': 1,
                'user_id': 2,
                'material_id': 1,
                'quantity': 1,
                'reason': '新员工入职需要办公桌',
                'status': 'pending',
                'request_date': datetime.now()
            },
            {
                'id': 2,
                'user_id': 3,
                'material_id': 2,
                'quantity': 1,
                'reason': '开发工作需要笔记本电脑',
                'status': 'approved',
                'request_date': datetime.now()
            }
        ]
        
        self._allocations = []
        self._next_id = {'users': 4, 'materials': 4, 'requests': 3, 'allocations': 1}
    
    def get_connection(self):
        """模拟获取连接"""
        return self
    
    def execute_query(self, sql, params=None):
        """模拟执行查询"""
        sql_lower = sql.lower().strip()
        
        if 'select * from users' in sql_lower:
            return self._users
        elif 'select * from materials' in sql_lower:
            return self._materials
        elif 'select * from material_requests' in sql_lower:
            return self._requests
        elif 'select count(*) from materials' in sql_lower:
            return [{'COUNT(*)': len(self._materials)}]
        elif 'select count(*) from users' in sql_lower:
            return [{'COUNT(*)': len(self._users)}]
        elif 'select count(*) from material_requests' in sql_lower:
            return [{'COUNT(*)': len(self._requests)}]
        else:
            logging.warning(f"未处理的查询: {sql}")
            return []
    
    def execute_query_one(self, sql, params=None):
        """模拟执行单条查询"""
        sql_lower = sql.lower().strip()
        
        if 'select * from users where username' in sql_lower and params:
            username = params[0]
            for user in self._users:
                if user['username'] == username:
                    return user
            return None
        elif 'select * from users where id' in sql_lower and params:
            user_id = params[0]
            for user in self._users:
                if user['id'] == user_id:
                    return user
            return None
        elif 'select * from materials where id' in sql_lower and params:
            material_id = params[0]
            for material in self._materials:
                if material['id'] == material_id:
                    return material
            return None
        else:
            result = self.execute_query(sql, params)
            return result[0] if result else None
    
    def execute_update(self, sql, params=None):
        """模拟执行更新"""
        logging.info(f"模拟更新: {sql} with params: {params}")
        return 1
    
    def execute_insert(self, sql, params=None):
        """模拟执行插入"""
        sql_lower = sql.lower().strip()
        
        if 'insert into material_requests' in sql_lower:
            new_id = self._next_id['requests']
            self._next_id['requests'] += 1
            
            new_request = {
                'id': new_id,
                'user_id': params[0] if params else 1,
                'material_id': params[1] if params and len(params) > 1 else 1,
                'quantity': params[2] if params and len(params) > 2 else 1,
                'reason': params[3] if params and len(params) > 3 else '测试申请',
                'status': 'pending',
                'request_date': datetime.now()
            }
            self._requests.append(new_request)
            return new_id
        
        logging.info(f"模拟插入: {sql} with params: {params}")
        return 1
    
    def close(self):
        """模拟关闭连接"""
        pass

# 创建模拟数据库实例
mock_db = MockDatabase()
