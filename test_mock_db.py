#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mock_database():
    """测试模拟数据库"""
    try:
        print("测试模拟数据库...")
        
        # 导入模拟数据库
        from dao.mock_database import mock_db
        print("✓ 模拟数据库导入成功")
        
        # 测试连接
        conn = mock_db.get_connection()
        print("✓ 模拟数据库连接成功")
        
        # 测试查询用户
        users = conn.execute_query("SELECT * FROM users")
        print(f"✓ 查询用户成功，共{len(users)}个用户")
        
        # 测试查询物资
        materials = conn.execute_query("SELECT * FROM materials")
        print(f"✓ 查询物资成功，共{len(materials)}个物资")
        
        # 测试单条查询
        admin_user = conn.execute_query_one("SELECT * FROM users WHERE username = %s", ['admin'])
        if admin_user:
            print(f"✓ 查询管理员用户成功: {admin_user['real_name']}")
        else:
            print("✗ 未找到管理员用户")
        
        print("✓ 模拟数据库测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 模拟数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_integration():
    """测试数据库集成"""
    try:
        print("\n测试数据库集成...")
        
        # 导入数据库类
        from dao.database import Database
        print("✓ 数据库类导入成功")
        
        # 创建数据库实例
        db = Database()
        print("✓ 数据库实例创建成功")
        
        # 获取连接（应该自动切换到模拟数据库）
        conn = db.get_connection()
        print("✓ 数据库连接获取成功")
        
        # 测试查询
        users = db.execute_query("SELECT * FROM users")
        print(f"✓ 通过数据库类查询用户成功，共{len(users)}个用户")
        
        print("✓ 数据库集成测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 数据库集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_service():
    """测试认证服务"""
    try:
        print("\n测试认证服务...")
        
        # 导入认证服务
        from services.auth_service import AuthService
        print("✓ 认证服务导入成功")
        
        # 创建认证服务实例
        auth_service = AuthService()
        print("✓ 认证服务实例创建成功")
        
        # 测试用户认证
        user = auth_service.authenticate('admin', 'admin123')
        if user:
            print(f"✓ 用户认证成功: {user.real_name}")
        else:
            print("✗ 用户认证失败")
        
        print("✓ 认证服务测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 认证服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== 系统组件测试 ===")
    
    success = True
    success &= test_mock_database()
    success &= test_database_integration()
    success &= test_auth_service()
    
    if success:
        print("\n=== 所有测试通过！ ===")
        print("现在可以启动应用：python app.py")
    else:
        print("\n=== 测试失败 ===")
        sys.exit(1)
