#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import sys

def setup_database():
    """设置数据库和用户"""
    
    # 首先尝试以不同的方式连接到MySQL
    connection_attempts = [
        {'host': 'localhost', 'user': 'root', 'password': ''},
        {'host': 'localhost', 'user': 'root', 'password': 'root'},
        {'host': 'localhost', 'user': 'root', 'password': '123456'},
        {'host': 'localhost', 'user': 'root', 'password': 'qyf20031211'},
    ]
    
    conn = None
    for attempt in connection_attempts:
        try:
            print(f"尝试连接MySQL: {attempt}")
            conn = pymysql.connect(**attempt)
            print("✓ 连接成功！")
            break
        except pymysql.Error as e:
            print(f"✗ 连接失败: {e}")
            continue
    
    if not conn:
        print("无法连接到MySQL服务器。请检查：")
        print("1. MySQL服务是否正在运行")
        print("2. root用户的密码是否正确")
        print("3. 尝试手动连接: mysql -u root -p")
        return False
    
    try:
        cursor = conn.cursor()
        
        # 创建数据库
        print("创建goods数据库...")
        cursor.execute("CREATE DATABASE IF NOT EXISTS goods CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✓ goods数据库创建成功")
        
        # 创建用户（如果不存在）
        print("设置数据库用户...")
        try:
            cursor.execute("CREATE USER IF NOT EXISTS 'goods_user'@'localhost' IDENTIFIED BY 'qyf20031211'")
            cursor.execute("GRANT ALL PRIVILEGES ON goods.* TO 'goods_user'@'localhost'")
            cursor.execute("FLUSH PRIVILEGES")
            print("✓ 用户goods_user创建成功")
        except pymysql.Error as e:
            print(f"用户创建警告: {e}")
        
        # 使用goods数据库
        cursor.execute("USE goods")
        
        # 读取并执行SQL初始化脚本
        print("执行数据库初始化脚本...")
        try:
            with open('database_init.sql', 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句并执行
            statements = sql_content.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                    except pymysql.Error as e:
                        if "already exists" not in str(e).lower():
                            print(f"SQL执行警告: {e}")
            
            conn.commit()
            print("✓ 数据库初始化完成")
            
        except FileNotFoundError:
            print("✗ 找不到database_init.sql文件")
            return False
        except Exception as e:
            print(f"✗ 执行SQL脚本失败: {e}")
            return False
        
        # 测试连接
        print("测试应用数据库连接...")
        test_conn = pymysql.connect(
            host='localhost',
            user='root',  # 使用root用户，因为我们知道它可以工作
            password=attempt['password'],  # 使用成功的密码
            database='goods',
            charset='utf8mb4'
        )
        test_cursor = test_conn.cursor()
        test_cursor.execute("SHOW TABLES")
        tables = test_cursor.fetchall()
        print(f"✓ 数据库连接测试成功，共有{len(tables)}个表")
        test_conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库设置失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("=== 数据库设置 ===")
    if setup_database():
        print("\n=== 数据库设置完成！ ===")
        print("现在可以运行应用了：python app.py")
    else:
        print("\n=== 数据库设置失败 ===")
        sys.exit(1)
