import requests
import json
import logging
from config import Config

class ChatService:
    def __init__(self):
        self.api_key = Config.DEEPSEEK_API_KEY
        self.api_url = Config.DEEPSEEK_API_URL
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def send_message(self, message, conversation_history=None):
        """
        发送消息到DeepSeek API并获取回复
        
        Args:
            message (str): 用户输入的消息
            conversation_history (list): 对话历史记录
            
        Returns:
            dict: 包含回复内容和状态的字典
        """
        try:
            # 构建消息列表
            messages = []
            
            # 添加系统提示
            system_prompt = """你是一个专业的金融企业物资管理系统助手。你可以帮助用户：
1. 解答物资管理相关的问题
2. 提供系统使用指导
3. 协助处理物资申请、分配等业务流程
4. 分析物资使用情况和统计数据
请用专业、友好的语气回答用户的问题。"""
            
            messages.append({
                "role": "system",
                "content": system_prompt
            })
            
            # 添加对话历史
            if conversation_history:
                messages.extend(conversation_history)
            
            # 添加当前用户消息
            messages.append({
                "role": "user",
                "content": message
            })
            
            # 构建请求数据
            data = {
                "model": "deepseek-chat",
                "messages": messages,
                "max_tokens": 1000,
                "temperature": 0.7,
                "stream": False
            }
            
            # 发送请求
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                reply = result['choices'][0]['message']['content']
                
                return {
                    'success': True,
                    'reply': reply,
                    'error': None
                }
            else:
                error_msg = f"API请求失败，状态码: {response.status_code}"
                logging.error(f"DeepSeek API错误: {error_msg}, 响应: {response.text}")
                return {
                    'success': False,
                    'reply': None,
                    'error': error_msg
                }
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时，请稍后重试"
            logging.error("DeepSeek API请求超时")
            return {
                'success': False,
                'reply': None,
                'error': error_msg
            }
            
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求错误: {str(e)}"
            logging.error(f"DeepSeek API网络错误: {e}")
            return {
                'success': False,
                'reply': None,
                'error': error_msg
            }
            
        except Exception as e:
            error_msg = f"系统错误: {str(e)}"
            logging.error(f"DeepSeek API系统错误: {e}")
            return {
                'success': False,
                'reply': None,
                'error': error_msg
            }
    
    def get_material_context(self, user_id):
        """
        获取用户相关的物资管理上下文信息
        
        Args:
            user_id (int): 用户ID
            
        Returns:
            str: 上下文信息
        """
        try:
            from dao.database import db
            
            # 获取用户信息
            user_sql = "SELECT username, real_name, role, department FROM users WHERE id = %s"
            user_info = db.execute_query_one(user_sql, (user_id,))
            
            if not user_info:
                return "用户信息未找到"
            
            # 获取部门物资统计
            dept_sql = """
                SELECT COUNT(*) as total_materials, 
                       SUM(quantity) as total_quantity
                FROM materials 
                WHERE department = %s
            """
            dept_stats = db.execute_query_one(dept_sql, (user_info['department'],))
            
            # 获取用户最近的申请记录
            request_sql = """
                SELECT COUNT(*) as total_requests
                FROM material_requests 
                WHERE user_id = %s AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            """
            request_stats = db.execute_query_one(request_sql, (user_id,))
            
            context = f"""
当前用户信息：
- 姓名：{user_info['real_name']}
- 部门：{user_info['department']}
- 角色：{user_info['role']}
- 部门物资总数：{dept_stats['total_materials'] if dept_stats else 0}
- 部门物资总量：{dept_stats['total_quantity'] if dept_stats else 0}
- 近30天申请次数：{request_stats['total_requests'] if request_stats else 0}
"""
            return context
            
        except Exception as e:
            logging.error(f"获取物资上下文失败: {e}")
            return "无法获取相关信息"

# 全局聊天服务实例
chat_service = ChatService()
