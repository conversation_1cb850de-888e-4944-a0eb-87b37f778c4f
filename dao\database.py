import pymysql
from config import Config
import logging

# 尝试导入模拟数据库
try:
    from .mock_database import mock_db
    MOCK_AVAILABLE = True
except ImportError:
    try:
        from dao.mock_database import mock_db
        MOCK_AVAILABLE = True
    except ImportError:
        MOCK_AVAILABLE = False
        mock_db = None

class Database:
    _instance = None
    _connection = None
    _use_mock = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance
    
    def get_connection(self):
        """获取数据库连接"""
        # 如果已经在使用模拟数据库，直接返回
        if self._use_mock and MOCK_AVAILABLE:
            return mock_db

        try:
            if self._connection is None or not self._connection.open:
                # 尝试多种数据库配置
                last_error = None
                for config in Config.DB_CONFIGS:
                    try:
                        self._connection = pymysql.connect(**config)
                        logging.info(f"数据库连接成功，使用配置: {config['user']}@{config['host']}")
                        self._use_mock = False
                        break
                    except Exception as e:
                        last_error = e
                        logging.warning(f"数据库连接失败，配置: {config['user']}@{config['host']}, 错误: {e}")
                        continue

                if self._connection is None:
                    # 如果所有MySQL连接都失败，尝试使用模拟数据库
                    if MOCK_AVAILABLE:
                        logging.warning("MySQL连接失败，切换到模拟数据库模式")
                        self._use_mock = True
                        return mock_db
                    else:
                        raise last_error or Exception("所有数据库配置都连接失败，且模拟数据库不可用")

            return self._connection
        except Exception as e:
            # 最后的备选方案：使用模拟数据库
            if MOCK_AVAILABLE and not self._use_mock:
                logging.error(f"数据库连接失败，切换到模拟数据库: {e}")
                self._use_mock = True
                return mock_db
            else:
                logging.error(f"数据库连接失败: {e}")
                raise
    
    def execute_query(self, sql, params=None):
        """执行查询语句"""
        connection = self.get_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise
    
    def execute_query_one(self, sql, params=None):
        """执行查询语句，返回单条记录"""
        connection = self.get_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchone()
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise
    
    def execute_update(self, sql, params=None):
        """执行更新语句"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                result = cursor.execute(sql, params)
                connection.commit()
                return result
        except Exception as e:
            connection.rollback()
            logging.error(f"更新执行失败: {e}")
            raise
    
    def execute_insert(self, sql, params=None):
        """执行插入语句，返回插入的ID"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                connection.commit()
                return cursor.lastrowid
        except Exception as e:
            connection.rollback()
            logging.error(f"插入执行失败: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None

# 全局数据库实例
db = Database()
